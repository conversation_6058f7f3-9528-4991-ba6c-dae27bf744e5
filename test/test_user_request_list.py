#!/usr/bin/env python3
"""
测试用户项目列表页面的用户请求列表功能
"""

import requests
import sys
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class UserRequestListTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        
    def test_user_request_list_display(self):
        """测试用户请求列表显示"""
        print("📋 测试用户请求列表显示...")
        
        try:
            response = requests.get(self.base_url)
            
            if response.status_code == 200:
                print("✅ 项目列表页面访问成功")
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找用户请求列表标题
                request_title = soup.find('h3', class_='h4')
                if request_title and '用户请求列表' in request_title.get_text():
                    print("✅ 找到用户请求列表标题")
                else:
                    print("❌ 未找到用户请求列表标题")
                    print(f"   页面内容包含: {'用户请求列表' in response.text}")
                    return False
                
                # 查找用户请求表格
                request_tables = soup.find_all('table', class_='table')
                if len(request_tables) >= 2:
                    print("✅ 找到用户请求表格")
                    
                    # 检查表格头部
                    request_table = request_tables[1]  # 第二个表格应该是用户请求表格
                    headers = request_table.find('thead')
                    if headers:
                        header_texts = [th.get_text().strip() for th in headers.find_all('th')]
                        expected_headers = ['项目名称', '用户邮箱', '官方网站', '状态', '申请时间']
                        
                        if all(header in header_texts for header in expected_headers):
                            print("✅ 用户请求表格头部正确")
                        else:
                            print(f"❌ 用户请求表格头部不正确，期望: {expected_headers}, 实际: {header_texts}")
                            return False
                    else:
                        print("❌ 用户请求表格缺少头部")
                        return False
                else:
                    print("❌ 未找到用户请求表格")
                    return False
                
                return True
            else:
                print(f"❌ 页面访问失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def test_search_functionality(self):
        """测试搜索功能"""
        print("🔍 测试搜索功能...")
        
        try:
            # 测试搜索框占位符文本
            response = requests.get(self.base_url)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找搜索框
                search_input = soup.find('input', {'name': 'search'})
                if search_input:
                    placeholder = search_input.get('placeholder', '')
                    if '用户邮箱' in placeholder:
                        print("✅ 搜索框占位符文本已更新")
                    else:
                        print(f"❌ 搜索框占位符文本未更新，当前: {placeholder}")
                        return False
                else:
                    print("❌ 未找到搜索框")
                    return False
                
                # 测试搜索功能
                search_url = f"{self.base_url}?search=test"
                search_response = requests.get(search_url)
                
                if search_response.status_code == 200:
                    print("✅ 搜索功能正常工作")
                    return True
                else:
                    print(f"❌ 搜索功能失败，状态码: {search_response.status_code}")
                    return False
            else:
                print(f"❌ 页面访问失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def test_create_and_display_request(self):
        """测试创建用户请求并在列表中显示"""
        print("📝 测试创建用户请求并在列表中显示...")
        
        try:
            # 创建测试请求
            request_data = {
                'email': '<EMAIL>',
                'project_name': 'Test User List Project',
                'official_website': 'https://test-user-list.example.com'
            }
            
            # 获取CSRF token
            response = requests.get(self.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_token = soup.find('meta', {'name': 'csrf-token'})
            if csrf_token:
                csrf_value = csrf_token.get('content')
            else:
                csrf_value = 'test-token'
            
            # 提交请求
            submit_response = requests.post(
                f"{self.base_url}/request-project",
                json=request_data,
                headers={
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrf_value
                },
                allow_redirects=False
            )
            
            if submit_response.status_code in [200, 302]:
                if submit_response.status_code == 200:
                    result = submit_response.json()
                    success = result.get('success')
                else:
                    # 302重定向表示成功
                    success = True

                if success:
                    print("✅ 用户请求创建成功")
                    
                    # 等待一下让数据库更新
                    import time
                    time.sleep(1)

                    # 检查请求是否在列表中显示
                    list_response = requests.get(self.base_url)
                    if list_response.status_code == 200:
                        list_soup = BeautifulSoup(list_response.text, 'html.parser')

                        # 查找创建的请求
                        if 'Test User List Project' in list_response.text:
                            print("✅ 创建的请求在列表中显示")
                            return True
                        else:
                            print("❌ 创建的请求未在列表中显示")
                            # 尝试搜索功能
                            search_response = requests.get(f"{self.base_url}?search=Test User List Project")
                            if search_response.status_code == 200 and 'Test User List Project' in search_response.text:
                                print("✅ 创建的请求可以通过搜索找到")
                                return True
                            else:
                                print("❌ 创建的请求无法通过搜索找到")
                                return False
                    else:
                        print(f"❌ 无法获取列表页面，状态码: {list_response.status_code}")
                        return False
                else:
                    if submit_response.status_code == 200:
                        result = submit_response.json()
                        print(f"❌ 请求创建失败: {result.get('errors', [])}")
                    else:
                        print(f"❌ 请求创建失败，状态码: {submit_response.status_code}")
                    return False
            else:
                print(f"❌ 请求提交失败，状态码: {submit_response.status_code}")
                try:
                    print(f"   响应内容: {submit_response.text[:200]}")
                except:
                    pass
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试用户请求列表功能...\n")
        
        tests = [
            self.test_user_request_list_display,
            self.test_search_functionality,
            self.test_create_and_display_request
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            print()
        
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！用户请求列表功能正常工作。")
            return True
        else:
            print("❌ 部分测试失败，请检查问题。")
            return False

if __name__ == '__main__':
    tester = UserRequestListTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

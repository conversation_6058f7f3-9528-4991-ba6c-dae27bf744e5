#!/usr/bin/env python3
"""
简单的已完成请求过滤功能验证
"""

import requests
import sys
from bs4 import BeautifulSoup

def test_completed_filter():
    """测试已完成请求过滤功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("🔍 测试已完成请求过滤功能...")
    
    try:
        # 获取主页
        response = requests.get(base_url)
        if response.status_code != 200:
            print(f"❌ 无法访问页面: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        content = response.text
        
        # 检查用户请求列表是否存在
        if "用户请求列表" not in content:
            print("❌ 页面中未找到用户请求列表")
            return False
        
        print("✅ 找到用户请求列表")
        
        # 查找用户请求表格
        tables = soup.find_all('table', class_='table')
        if len(tables) < 2:
            print("ℹ️  当前没有用户请求表格（可能没有请求数据）")
            return True
            
        request_table = tables[1]  # 第二个表格应该是用户请求表格
        rows = request_table.find('tbody').find_all('tr') if request_table.find('tbody') else []
        
        print(f"📊 当前显示的用户请求数量: {len(rows)}")
        
        # 检查是否有已完成状态的请求
        completed_count = 0
        other_status_count = 0
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 4:
                status_cell = cells[3]  # 状态列
                status_text = status_cell.get_text().strip()
                project_name = cells[0].get_text().strip()
                
                if '已完成' in status_text:
                    completed_count += 1
                    print(f"  ❌ 发现已完成请求: {project_name} (状态: {status_text})")
                else:
                    other_status_count += 1
                    print(f"  ✅ 非完成请求: {project_name} (状态: {status_text})")
        
        if completed_count == 0:
            print("✅ 过滤功能正常：没有已完成请求在列表中显示")
            return True
        else:
            print(f"❌ 过滤功能异常：发现 {completed_count} 个已完成请求仍在显示")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_test_request():
    """创建一个测试请求"""
    base_url = "http://127.0.0.1:5002"
    
    print("📝 创建测试请求...")
    
    request_data = {
        'email': '<EMAIL>',
        'project_name': 'Filter Test Project',
        'official_website': 'https://filter-test.example.com'
    }
    
    try:
        response = requests.post(
            f"{base_url}/request-project",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            allow_redirects=False
        )
        
        if response.status_code in [200, 302]:
            print("✅ 测试请求创建成功")
            return True
        else:
            print(f"❌ 测试请求创建失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试请求出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 已完成请求过滤功能验证")
    print("=" * 40)
    
    # 1. 创建测试请求
    create_test_request()
    
    # 2. 测试过滤功能
    if test_completed_filter():
        print("\n🎉 过滤功能验证通过！")
        print("💡 已完成的用户请求不会在列表中显示")
        return True
    else:
        print("\n❌ 过滤功能验证失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

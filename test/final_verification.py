#!/usr/bin/env python3
"""
用户请求列表功能最终验证脚本
"""

import requests
import sys
from bs4 import BeautifulSoup

class FinalVerification:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        
    def verify_page_structure(self):
        """验证页面结构"""
        print("🔍 验证页面结构...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code != 200:
                print(f"❌ 页面访问失败: {response.status_code}")
                return False
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 验证关键元素
            verifications = [
                ("页面标题", soup.find('h1', class_='h2') is not None),
                ("搜索框", soup.find('input', {'name': 'search'}) is not None),
                ("用户请求列表标题", soup.find('h3', class_='h4') is not None),
                ("项目报告表格", soup.find('table', class_='table') is not None),
                ("申请新项目按钮", soup.find('button', {'data-bs-target': '#requestModal'}) is not None)
            ]
            
            all_passed = True
            for name, passed in verifications:
                status = "✅" if passed else "❌"
                print(f"  {status} {name}")
                if not passed:
                    all_passed = False
                    
            return all_passed
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def verify_search_functionality(self):
        """验证搜索功能"""
        print("\n🔍 验证搜索功能...")
        
        search_tests = [
            ("空搜索", ""),
            ("项目名称搜索", "DeFi"),
            ("邮箱搜索", "alice"),
            ("不存在的内容", "nonexistent123")
        ]
        
        all_passed = True
        for test_name, search_term in search_tests:
            try:
                url = f"{self.base_url}?search={search_term}" if search_term else self.base_url
                response = requests.get(url)
                
                if response.status_code == 200:
                    print(f"  ✅ {test_name}: 搜索正常")
                else:
                    print(f"  ❌ {test_name}: 搜索失败 ({response.status_code})")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ {test_name}: 搜索出错 ({e})")
                all_passed = False
                
        return all_passed
    
    def verify_user_request_display(self):
        """验证用户请求显示"""
        print("\n🔍 验证用户请求显示...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code != 200:
                print("❌ 无法访问页面")
                return False
                
            soup = BeautifulSoup(response.text, 'html.parser')
            content = response.text
            
            # 检查用户请求相关元素
            checks = [
                ("用户请求列表标题", "用户请求列表" in content),
                ("用户邮箱列标题", "用户邮箱" in content),
                ("官方网站列标题", "官方网站" in content),
                ("状态列标题", "状态" in content),
                ("申请时间列标题", "申请时间" in content),
                ("搜索框占位符更新", "用户邮箱" in soup.find('input', {'name': 'search'}).get('placeholder', ''))
            ]
            
            all_passed = True
            for name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {name}")
                if not passed:
                    all_passed = False
                    
            # 检查是否有请求数据
            tables = soup.find_all('table', class_='table')
            if len(tables) >= 2:
                print("  ✅ 找到用户请求表格")
                
                # 检查表格内容
                request_table = tables[1]  # 第二个表格应该是用户请求表格
                rows = request_table.find('tbody').find_all('tr') if request_table.find('tbody') else []
                
                if rows:
                    print(f"  ✅ 用户请求表格有数据 ({len(rows)} 条)")
                    
                    # 检查第一行数据
                    first_row = rows[0]
                    cells = first_row.find_all('td')
                    if len(cells) >= 5:
                        print("  ✅ 用户请求数据格式正确")
                    else:
                        print("  ❌ 用户请求数据格式不正确")
                        all_passed = False
                else:
                    print("  ℹ️  用户请求表格暂无数据")
            else:
                print("  ❌ 未找到用户请求表格")
                all_passed = False
                
            return all_passed
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def verify_responsive_design(self):
        """验证响应式设计"""
        print("\n🔍 验证响应式设计...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code != 200:
                print("❌ 无法访问页面")
                return False
                
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查Bootstrap类
            responsive_checks = [
                ("响应式表格", soup.find('div', class_='table-responsive') is not None),
                ("Bootstrap卡片", soup.find('div', class_='card') is not None),
                ("Bootstrap按钮", soup.find('button', class_='btn') is not None),
                ("Bootstrap徽章", soup.find('span', class_='badge') is not None),
                ("Bootstrap网格", soup.find('div', class_='row') is not None)
            ]
            
            all_passed = True
            for name, passed in responsive_checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {name}")
                if not passed:
                    all_passed = False
                    
            return all_passed
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def run_verification(self):
        """运行完整验证"""
        print("🎯 用户请求列表功能最终验证")
        print("=" * 50)
        
        verifications = [
            ("页面结构", self.verify_page_structure),
            ("搜索功能", self.verify_search_functionality),
            ("用户请求显示", self.verify_user_request_display),
            ("响应式设计", self.verify_responsive_design)
        ]
        
        passed_count = 0
        total_count = len(verifications)
        
        for name, verify_func in verifications:
            if verify_func():
                passed_count += 1
                
        print(f"\n📊 验证结果: {passed_count}/{total_count} 通过")
        
        if passed_count == total_count:
            print("🎉 所有验证通过！用户请求列表功能完全正常。")
            print(f"\n🌐 功能访问地址:")
            print(f"   主页: {self.base_url}")
            print(f"   搜索示例: {self.base_url}?search=DeFi")
            return True
        else:
            print("❌ 部分验证失败，请检查问题。")
            return False

if __name__ == '__main__':
    verifier = FinalVerification()
    success = verifier.run_verification()
    sys.exit(0 if success else 1)

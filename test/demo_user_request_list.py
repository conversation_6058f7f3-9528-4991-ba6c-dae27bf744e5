#!/usr/bin/env python3
"""
用户请求列表功能演示脚本
"""

import requests
import json
import time
from urllib.parse import urljoin

class UserRequestListDemo:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        
    def create_demo_requests(self):
        """创建演示用的用户请求"""
        print("🚀 创建演示用户请求...")
        
        demo_requests = [
            {
                'email': '<EMAIL>',
                'project_name': 'DeFi Protocol Alpha',
                'official_website': 'https://defi-alpha.example.com'
            },
            {
                'email': '<EMAIL>', 
                'project_name': 'NFT Marketplace Beta',
                'official_website': 'https://nft-beta.example.com'
            },
            {
                'email': '<EMAIL>',
                'project_name': 'GameFi Platform Gamma',
                'official_website': 'https://gamefi-gamma.example.com'
            }
        ]
        
        for i, request_data in enumerate(demo_requests, 1):
            try:
                response = requests.post(
                    f"{self.base_url}/request-project",
                    json=request_data,
                    headers={'Content-Type': 'application/json'},
                    allow_redirects=False
                )
                
                if response.status_code in [200, 302]:
                    print(f"✅ 创建请求 {i}: {request_data['project_name']}")
                else:
                    print(f"❌ 创建请求 {i} 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 创建请求 {i} 出错: {e}")
                
            time.sleep(0.5)  # 避免请求过快
    
    def demo_search_functionality(self):
        """演示搜索功能"""
        print("\n🔍 演示搜索功能...")
        
        search_terms = [
            "DeFi",
            "<EMAIL>", 
            "NFT",
            "gamefi"
        ]
        
        for term in search_terms:
            try:
                response = requests.get(f"{self.base_url}?search={term}")
                if response.status_code == 200:
                    # 简单检查是否包含搜索词
                    if term.lower() in response.text.lower():
                        print(f"✅ 搜索 '{term}': 找到相关结果")
                    else:
                        print(f"❌ 搜索 '{term}': 未找到结果")
                else:
                    print(f"❌ 搜索 '{term}' 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 搜索 '{term}' 出错: {e}")
                
            time.sleep(0.5)
    
    def show_page_structure(self):
        """显示页面结构"""
        print("\n📄 页面结构分析...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code == 200:
                content = response.text
                
                # 检查关键元素
                checks = [
                    ("项目列表标题", "项目列表" in content),
                    ("搜索框", 'name="search"' in content),
                    ("用户请求列表标题", "用户请求列表" in content),
                    ("项目报告表格", "项目名称" in content and "创建时间" in content),
                    ("用户请求表格", "用户邮箱" in content and "申请时间" in content),
                    ("申请新项目按钮", "申请新项目" in content)
                ]
                
                for name, exists in checks:
                    status = "✅" if exists else "❌"
                    print(f"{status} {name}: {'存在' if exists else '不存在'}")
                    
                # 统计请求数量
                import re
                badge_match = re.search(r'badge bg-secondary[^>]*>(\d+)<', content)
                if badge_match:
                    count = badge_match.group(1)
                    print(f"📊 当前用户请求数量: {count}")
                else:
                    print("📊 未找到请求数量信息")
                    
            else:
                print(f"❌ 无法访问页面: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 页面分析出错: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎯 用户请求列表功能演示")
        print("=" * 50)
        
        # 1. 显示当前页面结构
        self.show_page_structure()
        
        # 2. 创建演示请求
        self.create_demo_requests()
        
        # 等待数据更新
        print("\n⏳ 等待数据更新...")
        time.sleep(2)
        
        # 3. 再次显示页面结构
        print("\n📄 更新后的页面结构...")
        self.show_page_structure()
        
        # 4. 演示搜索功能
        self.demo_search_functionality()
        
        # 5. 提供访问链接
        print(f"\n🌐 请访问以下链接查看完整效果:")
        print(f"   主页: {self.base_url}")
        print(f"   搜索DeFi: {self.base_url}?search=DeFi")
        print(f"   搜索邮箱: {self.base_url}?search=alice")
        
        print("\n✨ 演示完成！")
        print("💡 提示: 您可以在浏览器中打开上述链接查看实际效果")

if __name__ == '__main__':
    demo = UserRequestListDemo()
    demo.run_demo()

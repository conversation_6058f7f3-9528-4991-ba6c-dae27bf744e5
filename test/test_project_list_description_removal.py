#!/usr/bin/env python3
"""
测试用户项目列表页项目描述移除功能
"""

import requests
import sys
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class ProjectListDescriptionTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        
    def test_project_list_page(self):
        """测试项目列表页面"""
        print("📄 测试用户项目列表页面...")
        
        try:
            response = requests.get(self.base_url)
            
            if response.status_code == 200:
                print("✅ 项目列表页面访问成功")
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找项目列表表格
                table = soup.find('table', class_='table')
                if table:
                    print("✅ 找到项目列表表格")
                    
                    # 检查表格行
                    rows = table.find('tbody').find_all('tr') if table.find('tbody') else []
                    
                    if rows:
                        print(f"✅ 找到 {len(rows)} 个项目")
                        
                        # 检查每一行是否包含项目描述
                        description_found = False
                        for i, row in enumerate(rows):
                            # 查找项目名称单元格
                            name_cell = row.find('td')
                            if name_cell:
                                # 检查是否包含描述信息（通常在small标签中）
                                small_tags = name_cell.find_all('small')
                                if small_tags:
                                    for small in small_tags:
                                        if small.get_text().strip():
                                            description_found = True
                                            print(f"❌ 第{i+1}行仍包含描述信息: {small.get_text()[:50]}...")
                                            break
                        
                        if not description_found:
                            print("✅ 确认：项目列表中没有显示项目描述信息")
                            return True
                        else:
                            print("❌ 项目列表中仍然显示项目描述信息")
                            return False
                    else:
                        print("ℹ️ 项目列表为空，无法验证描述移除")
                        # 检查HTML源码中是否还有描述相关的代码
                        if 'report.description' in response.text:
                            print("❌ HTML源码中仍包含项目描述相关代码")
                            return False
                        else:
                            print("✅ HTML源码中已移除项目描述相关代码")
                            return True
                else:
                    print("⚠️ 未找到项目列表表格")
                    # 检查是否是空状态页面
                    if '暂无研究报告' in response.text or '没有找到匹配的项目' in response.text:
                        print("ℹ️ 页面显示空状态，检查HTML源码...")
                        if 'report.description' in response.text:
                            print("❌ HTML源码中仍包含项目描述相关代码")
                            return False
                        else:
                            print("✅ HTML源码中已移除项目描述相关代码")
                            return True
                    return False
            else:
                print(f"❌ 项目列表页面访问失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            return False
    
    def test_html_source_cleanup(self):
        """测试HTML源码清理"""
        print("\n🔍 检查HTML源码清理...")
        
        try:
            response = requests.get(self.base_url)
            
            if response.status_code == 200:
                html_content = response.text
                
                # 检查是否还有项目描述相关的代码
                description_patterns = [
                    'report.description',
                    '{% if report.description %}',
                    'text-muted">{{ report.description',
                    'report.description[:100]'
                ]
                
                found_patterns = []
                for pattern in description_patterns:
                    if pattern in html_content:
                        found_patterns.append(pattern)
                
                if found_patterns:
                    print("❌ HTML源码中仍包含以下项目描述相关代码:")
                    for pattern in found_patterns:
                        print(f"   - {pattern}")
                    return False
                else:
                    print("✅ HTML源码中已完全移除项目描述相关代码")
                    return True
            else:
                print(f"❌ 无法获取HTML源码，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ HTML源码检查过程中出现错误: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试用户项目列表页项目描述移除功能...")
        
        # 测试项目列表页面
        page_test = self.test_project_list_page()
        
        # 测试HTML源码清理
        source_test = self.test_html_source_cleanup()
        
        if page_test and source_test:
            print("\n🎉 所有测试通过！项目描述移除成功！")
            return True
        else:
            print("\n❌ 测试失败：项目描述移除不完整")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("用户项目列表页项目描述移除测试")
    print("=" * 60)
    
    tester = ProjectListDescriptionTest()
    
    try:
        success = tester.run_all_tests()
        if success:
            print("\n✅ 测试完成：项目描述移除验证成功")
            return 0
        else:
            print("\n❌ 测试失败：项目描述移除需要进一步检查")
            return 1
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())

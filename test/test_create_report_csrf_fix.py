#!/usr/bin/env python3
"""
测试管理员创建报告功能的CSRF修复
"""

import requests
import os
import sys
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class CreateReportCSRFTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        
    def get_csrf_token(self, url):
        """从页面获取CSRF token"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_meta = soup.find('meta', {'name': 'csrf-token'})
                if csrf_meta:
                    return csrf_meta.get('content')
        except Exception as e:
            print(f"获取CSRF token失败: {e}")
        return None
        
    def login_admin(self):
        """登录管理员账户"""
        print("🔐 登录管理员账户...")
        
        login_url = urljoin(self.base_url, '/admin/login')
        
        # 获取登录页面和CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
            return False
            
        self.csrf_token = self.get_csrf_token(login_url)
        
        # 登录
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'csrf_token': self.csrf_token
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if 'dashboard' in response.url.lower():
            print("✅ 管理员登录成功")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
    
    def test_create_report_page_access(self):
        """测试创建报告页面访问"""
        print("\n📄 测试创建报告页面访问...")
        
        create_url = urljoin(self.base_url, '/admin/reports/create')
        response = self.session.get(create_url)
        
        if response.status_code == 200:
            print("✅ 创建报告页面访问成功")
            
            # 检查页面是否包含CSRF token
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_meta = soup.find('meta', {'name': 'csrf-token'})
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            
            if csrf_meta:
                print("✅ 页面包含CSRF meta标签")
            else:
                print("❌ 页面缺少CSRF meta标签")
                
            if csrf_input:
                print("✅ 表单包含CSRF隐藏字段")
                return True
            else:
                print("❌ 表单缺少CSRF隐藏字段")
                return False
        else:
            print(f"❌ 创建报告页面访问失败，状态码: {response.status_code}")
            return False
    
    def test_create_report_without_files(self):
        """测试不带文件的创建报告（应该失败但不是因为CSRF）"""
        print("\n📝 测试不带文件的创建报告...")
        
        create_url = urljoin(self.base_url, '/admin/reports/create')
        
        # 获取最新的CSRF token
        response = self.session.get(create_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        csrf_token = csrf_input.get('value') if csrf_input else None
        
        if not csrf_token:
            print("❌ 无法获取CSRF token")
            return False
        
        # 提交表单（不包含文件）
        form_data = {
            'csrf_token': csrf_token,
            'project_name': 'CSRF测试项目',
            'official_website': 'https://example.com',
            'creator_name': 'CSRF测试员',
            'description': '这是一个CSRF修复测试'
        }
        
        response = self.session.post(create_url, data=form_data, allow_redirects=False)
        
        # 检查响应
        if response.status_code == 200:
            # 应该返回到表单页面，显示文件验证错误
            if '请上传有效的' in response.text or '文件' in response.text:
                print("✅ CSRF验证通过，显示文件验证错误（符合预期）")
                return True
            else:
                print("⚠️ CSRF验证通过，但没有显示预期的文件验证错误")
                return True
        elif response.status_code == 400 and 'CSRF' in response.text:
            print("❌ CSRF验证失败")
            return False
        else:
            print(f"⚠️ 意外的响应状态码: {response.status_code}")
            return True  # 不是CSRF错误就算成功
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试管理员创建报告功能的CSRF修复...")
        
        # 登录
        if not self.login_admin():
            return False
        
        # 测试创建报告页面
        if not self.test_create_report_page_access():
            return False
        
        # 测试CSRF保护
        if not self.test_create_report_without_files():
            return False
        
        print("\n🎉 所有测试通过！CSRF修复成功！")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("管理员创建报告功能CSRF修复测试")
    print("=" * 60)
    
    tester = CreateReportCSRFTest()
    
    try:
        success = tester.run_all_tests()
        if success:
            print("\n✅ 测试完成：CSRF修复验证成功")
            return 0
        else:
            print("\n❌ 测试失败：CSRF修复需要进一步检查")
            return 1
    except Exception as e:
        print(f"\n💥 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())

#!/usr/bin/env python3
"""
全面的已完成请求过滤功能测试
"""

import requests
import sys
import time
from bs4 import BeautifulSoup

def test_filter_functionality():
    """测试过滤功能"""
    base_url = "http://127.0.0.1:5002"
    
    print("🎯 全面测试已完成请求过滤功能")
    print("=" * 50)
    
    # 1. 创建多个测试请求
    print("📝 创建测试请求...")
    test_requests = [
        {
            'email': '<EMAIL>',
            'project_name': 'Pending Project',
            'official_website': 'https://pending.example.com'
        },
        {
            'email': '<EMAIL>',
            'project_name': 'Approved Project',
            'official_website': 'https://approved.example.com'
        },
        {
            'email': '<EMAIL>',
            'project_name': 'Rejected Project',
            'official_website': 'https://rejected.example.com'
        }
    ]
    
    created_count = 0
    for i, request_data in enumerate(test_requests, 1):
        try:
            response = requests.post(
                f"{base_url}/request-project",
                json=request_data,
                headers={'Content-Type': 'application/json'},
                allow_redirects=False
            )
            
            if response.status_code in [200, 302]:
                print(f"  ✅ 创建请求 {i}: {request_data['project_name']}")
                created_count += 1
            else:
                print(f"  ❌ 创建请求 {i} 失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 创建请求 {i} 出错: {e}")
            
        time.sleep(0.5)
    
    print(f"📊 成功创建 {created_count} 个测试请求")
    
    # 2. 检查请求显示
    print("\n🔍 检查请求显示...")
    time.sleep(1)  # 等待数据更新
    
    try:
        response = requests.get(base_url)
        if response.status_code != 200:
            print(f"❌ 无法访问页面: {response.status_code}")
            return False
            
        soup = BeautifulSoup(response.text, 'html.parser')
        content = response.text
        
        # 检查用户请求列表是否存在
        if "用户请求列表" not in content:
            print("❌ 页面中未找到用户请求列表")
            return False
        
        print("✅ 找到用户请求列表")
        
        # 查找用户请求表格
        tables = soup.find_all('table', class_='table')
        if len(tables) < 2:
            print("ℹ️  当前没有用户请求表格数据")
            # 检查是否有"暂无用户请求"的提示
            if "暂无用户请求" in content:
                print("✅ 正确显示暂无请求提示")
            return True
            
        request_table = tables[1]  # 第二个表格应该是用户请求表格
        rows = request_table.find('tbody').find_all('tr') if request_table.find('tbody') else []
        
        print(f"📊 当前显示的用户请求数量: {len(rows)}")
        
        # 检查状态分布
        status_count = {'pending': 0, 'approved': 0, 'completed': 0, 'rejected': 0, 'other': 0}
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 4:
                status_cell = cells[3]  # 状态列
                status_text = status_cell.get_text().strip()
                project_name = cells[0].get_text().strip()
                
                if '待处理' in status_text:
                    status_count['pending'] += 1
                    print(f"  ✅ 待处理请求: {project_name}")
                elif '已批准' in status_text:
                    status_count['approved'] += 1
                    print(f"  ✅ 已批准请求: {project_name}")
                elif '已完成' in status_text:
                    status_count['completed'] += 1
                    print(f"  ❌ 已完成请求仍在显示: {project_name}")
                elif '已拒绝' in status_text:
                    status_count['rejected'] += 1
                    print(f"  ✅ 已拒绝请求: {project_name}")
                else:
                    status_count['other'] += 1
                    print(f"  ℹ️  其他状态请求: {project_name} (状态: {status_text})")
        
        print(f"\n📊 状态分布:")
        print(f"  待处理: {status_count['pending']}")
        print(f"  已批准: {status_count['approved']}")
        print(f"  已完成: {status_count['completed']} ← 应该为0")
        print(f"  已拒绝: {status_count['rejected']}")
        print(f"  其他: {status_count['other']}")
        
        # 验证过滤功能
        if status_count['completed'] == 0:
            print("\n✅ 过滤功能正常：没有已完成请求在列表中显示")
            filter_success = True
        else:
            print(f"\n❌ 过滤功能异常：发现 {status_count['completed']} 个已完成请求仍在显示")
            filter_success = False
        
        # 3. 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_terms = ['Pending', 'Approved', 'Rejected', 'Project']
        
        search_success = True
        for term in search_terms:
            try:
                search_response = requests.get(f"{base_url}?search={term}")
                if search_response.status_code == 200:
                    search_soup = BeautifulSoup(search_response.text, 'html.parser')
                    search_tables = search_soup.find_all('table', class_='table')
                    
                    if len(search_tables) >= 2:
                        search_table = search_tables[1]
                        search_rows = search_table.find('tbody').find_all('tr') if search_table.find('tbody') else []
                        
                        # 检查搜索结果中是否有已完成请求
                        completed_in_search = 0
                        for row in search_rows:
                            cells = row.find_all('td')
                            if len(cells) >= 4:
                                status_cell = cells[3]
                                if '已完成' in status_cell.get_text():
                                    completed_in_search += 1
                        
                        if completed_in_search == 0:
                            print(f"  ✅ 搜索 '{term}': 正确排除已完成请求")
                        else:
                            print(f"  ❌ 搜索 '{term}': 发现 {completed_in_search} 个已完成请求")
                            search_success = False
                    else:
                        print(f"  ℹ️  搜索 '{term}': 无结果")
                else:
                    print(f"  ❌ 搜索 '{term}' 失败: {search_response.status_code}")
                    search_success = False
                    
            except Exception as e:
                print(f"  ❌ 搜索 '{term}' 出错: {e}")
                search_success = False
        
        return filter_success and search_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    if test_filter_functionality():
        print("\n🎉 所有测试通过！")
        print("💡 已完成的用户请求正确地被过滤，不在列表中显示")
        print(f"🌐 查看效果: http://127.0.0.1:5002")
        return True
    else:
        print("\n❌ 测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

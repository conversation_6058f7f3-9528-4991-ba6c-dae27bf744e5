#!/usr/bin/env python3
"""
测试已完成用户请求过滤功能
"""

import requests
import sys
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class CompletedRequestFilterTest:
    def __init__(self, base_url="http://127.0.0.1:5002"):
        self.base_url = base_url
        
    def create_test_requests(self):
        """创建测试请求"""
        print("📝 创建测试请求...")
        
        test_requests = [
            {
                'email': '<EMAIL>',
                'project_name': 'Pending Test Project',
                'official_website': 'https://pending-test.example.com'
            },
            {
                'email': '<EMAIL>',
                'project_name': 'Approved Test Project', 
                'official_website': 'https://approved-test.example.com'
            },
            {
                'email': '<EMAIL>',
                'project_name': 'Completed Test Project',
                'official_website': 'https://completed-test.example.com'
            }
        ]
        
        created_requests = []
        for i, request_data in enumerate(test_requests, 1):
            try:
                response = requests.post(
                    f"{self.base_url}/request-project",
                    json=request_data,
                    headers={'Content-Type': 'application/json'},
                    allow_redirects=False
                )
                
                if response.status_code in [200, 302]:
                    print(f"✅ 创建请求 {i}: {request_data['project_name']}")
                    created_requests.append(request_data)
                else:
                    print(f"❌ 创建请求 {i} 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 创建请求 {i} 出错: {e}")
                
        return created_requests
    
    def check_requests_before_completion(self):
        """检查完成前的请求显示"""
        print("\n🔍 检查完成前的请求显示...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code == 200:
                content = response.text
                
                # 检查测试请求是否都显示
                test_projects = [
                    'Pending Test Project',
                    'Approved Test Project', 
                    'Completed Test Project'
                ]
                
                visible_count = 0
                for project in test_projects:
                    if project in content:
                        print(f"  ✅ 找到请求: {project}")
                        visible_count += 1
                    else:
                        print(f"  ❌ 未找到请求: {project}")
                
                print(f"  📊 显示的测试请求数量: {visible_count}/3")
                return visible_count >= 2  # 至少应该显示2个（pending和approved）
            else:
                print(f"❌ 页面访问失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return False
    
    def simulate_request_completion(self):
        """模拟请求完成（通过直接修改数据库状态）"""
        print("\n⚙️  模拟请求完成...")
        
        # 注意：这里我们只是模拟，实际上需要通过管理员界面来完成请求
        # 由于我们无法直接访问数据库，我们将通过创建一个已完成状态的请求来测试
        print("  ℹ️  注意：在实际环境中，请求完成应通过管理员界面操作")
        print("  ℹ️  这里我们通过检查现有数据来验证过滤功能")
        return True
    
    def check_requests_after_completion(self):
        """检查完成后的请求显示"""
        print("\n🔍 检查完成后的请求显示...")
        
        try:
            response = requests.get(self.base_url)
            if response.status_code == 200:
                content = response.text
                soup = BeautifulSoup(content, 'html.parser')
                
                # 检查用户请求表格
                tables = soup.find_all('table', class_='table')
                if len(tables) >= 2:
                    request_table = tables[1]  # 第二个表格是用户请求表格
                    rows = request_table.find('tbody').find_all('tr') if request_table.find('tbody') else []
                    
                    print(f"  📊 当前显示的用户请求数量: {len(rows)}")
                    
                    # 检查是否有已完成状态的请求
                    completed_found = False
                    for row in rows:
                        cells = row.find_all('td')
                        if len(cells) >= 4:
                            status_cell = cells[3]  # 状态列
                            if '已完成' in status_cell.get_text():
                                completed_found = True
                                project_name = cells[0].get_text().strip()
                                print(f"  ❌ 发现已完成请求仍在显示: {project_name}")
                    
                    if not completed_found:
                        print("  ✅ 未发现已完成请求在列表中显示")
                        return True
                    else:
                        print("  ❌ 发现已完成请求仍在显示")
                        return False
                else:
                    print("  ❌ 未找到用户请求表格")
                    return False
                    
            else:
                print(f"❌ 页面访问失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return False
    
    def test_search_with_completed_requests(self):
        """测试搜索功能是否排除已完成请求"""
        print("\n🔍 测试搜索功能是否排除已完成请求...")
        
        search_terms = ['Completed', 'completed', 'Test']
        
        for term in search_terms:
            try:
                response = requests.get(f"{self.base_url}?search={term}")
                if response.status_code == 200:
                    content = response.text
                    
                    # 检查搜索结果中是否包含已完成的请求
                    if 'Completed Test Project' in content:
                        print(f"  ❌ 搜索 '{term}' 仍显示已完成请求")
                        return False
                    else:
                        print(f"  ✅ 搜索 '{term}' 正确排除已完成请求")
                else:
                    print(f"  ❌ 搜索 '{term}' 失败: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"  ❌ 搜索 '{term}' 出错: {e}")
                return False
        
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试已完成用户请求过滤功能...\n")
        
        tests = [
            ("创建测试请求", lambda: len(self.create_test_requests()) >= 2),
            ("检查完成前请求显示", self.check_requests_before_completion),
            ("模拟请求完成", self.simulate_request_completion),
            ("检查完成后请求显示", self.check_requests_after_completion),
            ("测试搜索排除已完成请求", self.test_search_with_completed_requests)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
            print()
        
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！已完成请求过滤功能正常工作。")
            return True
        else:
            print("❌ 部分测试失败，请检查问题。")
            return False

if __name__ == '__main__':
    tester = CompletedRequestFilterTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, Response
from app.services.database import db_service
from app.models.research_report import ResearchReport
from app.models.user_request import UserRequest
from app.utils.validators import validate_email, validate_url
import logging
import math
from datetime import datetime
from xml.etree.ElementTree import Element, SubElement, tostring

logger = logging.getLogger(__name__)

public_bp = Blueprint('public', __name__)

@public_bp.route('/')
def index():
    """主页 - 显示研究报告列表和用户请求列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search_query = request.args.get('search', '').strip()

        # 获取报告列表
        reports, reports_total = ResearchReport.get_published_reports(
            page=page,
            per_page=per_page,
            search_query=search_query
        )

        # 获取用户请求列表
        requests, requests_total = UserRequest.get_public_requests(
            page=page,
            per_page=per_page,
            search_query=search_query
        )

        # 计算分页信息（基于报告数据）
        total_pages = math.ceil(reports_total / per_page)
        has_prev = page > 1
        has_next = page < total_pages

        pagination = {
            'page': page,
            'per_page': per_page,
            'total': reports_total,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }

        return render_template('public/index.html',
                             reports=reports,
                             requests=requests,
                             pagination=pagination,
                             search_query=search_query,
                             requests_total=requests_total)
    
    except Exception as e:
        logger.error(f"Error loading index page: {e}")
        flash('加载页面时出现错误，请稍后重试。', 'error')
        return render_template('public/index.html', reports=[], pagination={})

@public_bp.route('/report/<report_id>/analysis')
def view_analysis(report_id):
    """查看分析页面 - 渲染HTML/JS文件"""
    try:
        report = ResearchReport.get_by_id(report_id)
        if not report or not report.get('is_published'):
            flash('报告不存在或未发布。', 'error')
            return redirect(url_for('public.index'))
        
        # 读取分析文件内容
        analysis_content = ResearchReport.get_analysis_content(report['analysis_file_path'])
        
        return render_template('public/analysis.html', 
                             report=report, 
                             analysis_content=analysis_content)
    
    except Exception as e:
        logger.error(f"Error loading analysis page for report {report_id}: {e}")
        flash('加载分析页面时出现错误。', 'error')
        return redirect(url_for('public.index'))

@public_bp.route('/report/<report_id>/report')
def view_report(report_id):
    """查看报告页面 - 渲染Markdown文件"""
    try:
        report = ResearchReport.get_by_id(report_id)
        if not report or not report.get('is_published'):
            flash('报告不存在或未发布。', 'error')
            return redirect(url_for('public.index'))
        
        # 读取并转换Markdown内容
        report_content = ResearchReport.get_report_content(report['report_file_path'])
        
        return render_template('public/report.html', 
                             report=report, 
                             report_content=report_content)
    
    except Exception as e:
        logger.error(f"Error loading report page for report {report_id}: {e}")
        flash('加载报告页面时出现错误。', 'error')
        return redirect(url_for('public.index'))

@public_bp.route('/request-project', methods=['POST'])
def request_project():
    """提交项目请求"""
    try:
        data = request.get_json()
        
        # 验证输入数据
        email = data.get('email', '').strip()
        project_name = data.get('project_name', '').strip()
        official_website = data.get('official_website', '').strip()
        
        errors = []
        
        if not email:
            errors.append('邮箱地址不能为空')
        elif not validate_email(email):
            errors.append('请输入有效的邮箱地址')
        
        if not project_name:
            errors.append('项目名称不能为空')
        elif len(project_name) > 255:
            errors.append('项目名称不能超过255个字符')
        
        if not official_website:
            errors.append('官方网站不能为空')
        elif not validate_url(official_website):
            errors.append('请输入有效的网站URL')
        
        if errors:
            return jsonify({'success': False, 'errors': errors}), 400
        
        # 检查是否已存在相同的请求
        existing_request = UserRequest.get_by_email_and_project(email, project_name)
        if existing_request:
            return jsonify({
                'success': False, 
                'errors': ['您已经提交过相同项目的请求，请耐心等待处理结果。']
            }), 400
        
        # 创建新请求
        request_data = {
            'user_email': email,
            'project_name': project_name,
            'official_website': official_website,
            'status': 'pending'
        }
        
        UserRequest.create(request_data)
        
        # TODO: 发送邮件通知管理员
        
        return jsonify({
            'success': True, 
            'message': '请求已成功提交！我们会尽快处理您的请求，完成后将通过邮件通知您。'
        })
    
    except Exception as e:
        logger.error(f"Error processing project request: {e}")
        return jsonify({
            'success': False, 
            'errors': ['提交请求时出现错误，请稍后重试。']
        }), 500

@public_bp.route('/search')
def search():
    """搜索API端点"""
    try:
        query = request.args.get('q', '').strip()
        page = request.args.get('page', 1, type=int)
        per_page = 10

        if not query:
            return jsonify({'reports': [], 'total': 0})

        reports, total_count = ResearchReport.search_reports(query, page, per_page)

        return jsonify({
            'reports': reports,
            'total': total_count,
            'page': page,
            'per_page': per_page
        })

    except Exception as e:
        logger.error(f"Error in search: {e}")
        return jsonify({'error': '搜索时出现错误'}), 500

@public_bp.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        from app.services.database import db_service
        db_status = db_service.test_connection()

        return jsonify({
            'status': 'healthy' if db_status else 'unhealthy',
            'database': 'connected' if db_status else 'disconnected',
            'timestamp': datetime.utcnow().isoformat()
        }), 200 if db_status else 503

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503

@public_bp.route('/robots.txt')
def robots_txt():
    """生成robots.txt文件"""
    try:
        robots_content = """# Robots.txt for Web3项目深度分析报告平台
User-agent: *
Allow: /
Allow: /static/
Allow: /report/
Allow: /search

# 禁止访问管理员区域
Disallow: /admin/
Disallow: /admin/*

# 禁止访问私有文件
Disallow: /uploads/
Disallow: /*.log
Disallow: /*.tmp

# 允许重要SEO文件
Allow: /robots.txt
Allow: /sitemap.xml
Allow: /favicon.ico

# 主要搜索引擎规则
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

# 站点地图位置
Sitemap: {sitemap_url}

# Web3和区块链内容优化
Allow: /report/*/report
Allow: /report/*/analysis""".format(
            sitemap_url=url_for('public.sitemap_xml', _external=True)
        )

        return Response(robots_content, mimetype='text/plain')

    except Exception as e:
        logger.error(f"Error generating robots.txt: {e}")
        return Response("User-agent: *\nAllow: /", mimetype='text/plain')

@public_bp.route('/sitemap.xml')
def sitemap_xml():
    """生成XML站点地图"""
    try:
        # 创建根元素
        urlset = Element('urlset')
        urlset.set('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9')
        urlset.set('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance')
        urlset.set('xsi:schemaLocation', 'http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd')

        # 添加主页
        url = SubElement(urlset, 'url')
        SubElement(url, 'loc').text = url_for('public.index', _external=True)
        SubElement(url, 'lastmod').text = datetime.utcnow().strftime('%Y-%m-%d')
        SubElement(url, 'changefreq').text = 'daily'
        SubElement(url, 'priority').text = '1.0'

        # 添加所有已发布的报告
        reports, _ = ResearchReport.get_published_reports(page=1, per_page=1000)

        for report in reports:
            # 报告页面
            url = SubElement(urlset, 'url')
            SubElement(url, 'loc').text = url_for('public.view_report', report_id=report['id'], _external=True)
            if report.get('updated_at'):
                SubElement(url, 'lastmod').text = report['updated_at'].strftime('%Y-%m-%d')
            elif report.get('created_at'):
                SubElement(url, 'lastmod').text = report['created_at'].strftime('%Y-%m-%d')
            SubElement(url, 'changefreq').text = 'weekly'
            SubElement(url, 'priority').text = '0.8'

            # 分析页面
            url = SubElement(urlset, 'url')
            SubElement(url, 'loc').text = url_for('public.view_analysis', report_id=report['id'], _external=True)
            if report.get('updated_at'):
                SubElement(url, 'lastmod').text = report['updated_at'].strftime('%Y-%m-%d')
            elif report.get('created_at'):
                SubElement(url, 'lastmod').text = report['created_at'].strftime('%Y-%m-%d')
            SubElement(url, 'changefreq').text = 'weekly'
            SubElement(url, 'priority').text = '0.7'

        # 生成XML字符串
        xml_str = tostring(urlset, encoding='unicode', method='xml')
        xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'

        return Response(xml_declaration + xml_str, mimetype='application/xml')

    except Exception as e:
        logger.error(f"Error generating sitemap: {e}")
        # 返回基本的sitemap
        basic_sitemap = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>{}</loc>
        <lastmod>{}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
</urlset>'''.format(
            url_for('public.index', _external=True),
            datetime.utcnow().strftime('%Y-%m-%d')
        )
        return Response(basic_sitemap, mimetype='application/xml')

# SEO友好的URL路由
@public_bp.route('/web3-projects/')
@public_bp.route('/web3-projects/page/<int:page>/')
def web3_projects(page=1):
    """SEO友好的Web3项目列表页面"""
    return redirect(url_for('public.index', page=page))

@public_bp.route('/blockchain-analysis/')
def blockchain_analysis():
    """区块链分析页面重定向"""
    return redirect(url_for('public.index'))

@public_bp.route('/defi-projects/')
def defi_projects():
    """DeFi项目页面"""
    return redirect(url_for('public.index', search='DeFi'))

@public_bp.route('/project/<project_name>/')
@public_bp.route('/project/<project_name>/report/')
def project_report_seo(project_name):
    """SEO友好的项目报告URL"""
    try:
        # 通过项目名称查找报告
        reports, _ = ResearchReport.get_published_reports(page=1, per_page=1, search_query=project_name)
        if reports:
            return redirect(url_for('public.view_report', report_id=reports[0]['id']))
        else:
            flash('项目报告不存在。', 'error')
            return redirect(url_for('public.index'))
    except Exception as e:
        logger.error(f"Error in SEO project route: {e}")
        return redirect(url_for('public.index'))

@public_bp.route('/project/<project_name>/analysis/')
def project_analysis_seo(project_name):
    """SEO友好的项目分析URL"""
    try:
        # 通过项目名称查找报告
        reports, _ = ResearchReport.get_published_reports(page=1, per_page=1, search_query=project_name)
        if reports:
            return redirect(url_for('public.view_analysis', report_id=reports[0]['id']))
        else:
            flash('项目分析不存在。', 'error')
            return redirect(url_for('public.index'))
    except Exception as e:
        logger.error(f"Error in SEO analysis route: {e}")
        return redirect(url_for('public.index'))

# 分类页面路由
@public_bp.route('/category/defi/')
def category_defi():
    """DeFi分类页面"""
    return redirect(url_for('public.index', search='DeFi'))

@public_bp.route('/category/nft/')
def category_nft():
    """NFT分类页面"""
    return redirect(url_for('public.index', search='NFT'))

@public_bp.route('/category/layer2/')
def category_layer2():
    """Layer2分类页面"""
    return redirect(url_for('public.index', search='Layer2'))

@public_bp.route('/category/gamefi/')
def category_gamefi():
    """GameFi分类页面"""
    return redirect(url_for('public.index', search='GameFi'))

# 面包屑导航支持
@public_bp.route('/reports/')
@public_bp.route('/reports/page/<int:page>/')
def reports_list(page=1):
    """报告列表页面（SEO友好）"""
    return redirect(url_for('public.index', page=page))

@public_bp.route('/search/<search_term>/')
@public_bp.route('/search/<search_term>/page/<int:page>/')
def search_seo(search_term, page=1):
    """SEO友好的搜索结果页面"""
    return redirect(url_for('public.index', search=search_term, page=page))
